---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==


# Text Elements
DynamicDataSource → ShardingSphere-JDBC 平滑迁移 ^title-text

现有方案

• DynamicDataSource
• 只支持分库
• 自研维护成本高 ^current-text

核心问题

• 需要分表能力
• 架构冲突
• 无法共存 ^problem-text

解决方案

• ShardingSphere-JDBC
• 支持分库分表
• 企业级特性 ^solution-text

第一阶段：平滑迁移「只做分库，不做分表」 ^phase1-title-text

多配置源统一管理

• Nacos配置中心
• APS Dubbo服务
• 配置合并策略
• 编程式构建 ^config-text

统一AOP路由机制

• ServiceContext获取
• HintManager路由
• 融担号分库算法
• 业务代码零侵入 ^aop-text

功能对等性验证

• 路由一致性测试
• 性能对比测试
• 事务一致性测试
• 迁移验证机制 ^validation-text

架构统一收益

• 技术栈标准化
• 分表能力准备
• 企业级特性
• 平滑过渡保障 ^benefits-text

第二阶段：库内分表改造
「在第一阶段基础上增加分表能力」 ^phase2-text

# Drawing
```json
{
	"type": "excalidraw",
	"version": 2,
	"source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
	"elements": [
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "title-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 200,
			"y": 50,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "#a5d8ff",
			"width": 400,
			"height": 60,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "title-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "title-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 220,
			"y": 70,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 360,
			"height": 25,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 20,
			"fontFamily": 1,
			"text": "DynamicDataSource → ShardingSphere-JDBC 平滑迁移",
			"textAlign": "center",
			"verticalAlign": "middle",
			"containerId": "title-rect",
			"originalText": "DynamicDataSource → ShardingSphere-JDBC 平滑迁移",
			"lineHeight": 1.25,
			"baseline": 18
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "current-state",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 50,
			"y": 150,
			"strokeColor": "#e03131",
			"backgroundColor": "#ffc9c9",
			"width": 200,
			"height": 120,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "current-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "current-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 70,
			"y": 170,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 160,
			"height": 80,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 16,
			"fontFamily": 1,
			"text": "现有方案\n\n• DynamicDataSource\n• 只支持分库\n• 自研维护成本高",
			"textAlign": "left",
			"verticalAlign": "top",
			"containerId": "current-state",
			"originalText": "现有方案\n\n• DynamicDataSource\n• 只支持分库\n• 自研维护成本高",
			"lineHeight": 1.25,
			"baseline": 73
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "problem-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 350,
			"y": 150,
			"strokeColor": "#f08c00",
			"backgroundColor": "#ffd43b",
			"width": 200,
			"height": 120,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "problem-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "problem-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 370,
			"y": 170,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 160,
			"height": 80,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 16,
			"fontFamily": 1,
			"text": "核心问题\n\n• 需要分表能力\n• 架构冲突\n• 无法共存",
			"textAlign": "left",
			"verticalAlign": "top",
			"containerId": "problem-rect",
			"originalText": "核心问题\n\n• 需要分表能力\n• 架构冲突\n• 无法共存",
			"lineHeight": 1.25,
			"baseline": 73
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "solution-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 650,
			"y": 150,
			"strokeColor": "#2f9e44",
			"backgroundColor": "#c3fae8",
			"width": 200,
			"height": 120,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "solution-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "solution-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 670,
			"y": 170,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 160,
			"height": 80,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 16,
			"fontFamily": 1,
			"text": "解决方案\n\n• ShardingSphere-JDBC\n• 支持分库分表\n• 企业级特性",
			"textAlign": "left",
			"verticalAlign": "top",
			"containerId": "solution-rect",
			"originalText": "解决方案\n\n• ShardingSphere-JDBC\n• 支持分库分表\n• 企业级特性",
			"lineHeight": 1.25,
			"baseline": 73
		},
		{
			"type": "arrow",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "arrow1",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 250,
			"y": 210,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 100,
			"height": 0,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 2
			},
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"startBinding": null,
			"endBinding": null,
			"lastCommittedPoint": null,
			"startArrowhead": null,
			"endArrowhead": "arrow",
			"points": [
				[0, 0],
				[100, 0]
			]
		},
		{
			"type": "arrow",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "arrow2",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 550,
			"y": 210,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 100,
			"height": 0,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 2
			},
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"startBinding": null,
			"endBinding": null,
			"lastCommittedPoint": null,
			"startArrowhead": null,
			"endArrowhead": "arrow",
			"points": [
				[0, 0],
				[100, 0]
			]
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "phase1-title",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 100,
			"y": 320,
			"strokeColor": "#1971c2",
			"backgroundColor": "#d0ebff",
			"width": 700,
			"height": 40,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "phase1-title-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "phase1-title-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 120,
			"y": 335,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 660,
			"height": 18,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 18,
			"fontFamily": 1,
			"text": "第一阶段：平滑迁移「只做分库，不做分表」",
			"textAlign": "center",
			"verticalAlign": "middle",
			"containerId": "phase1-title",
			"originalText": "第一阶段：平滑迁移「只做分库，不做分表」",
			"lineHeight": 1.25,
			"baseline": 13
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "config-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 50,
			"y": 400,
			"strokeColor": "#7048e8",
			"backgroundColor": "#e5dbff",
			"width": 180,
			"height": 140,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "config-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "config-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 65,
			"y": 420,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 150,
			"height": 100,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 14,
			"fontFamily": 1,
			"text": "多配置源统一管理\n\n• Nacos配置中心\n• APS Dubbo服务\n• 配置合并策略\n• 编程式构建",
			"textAlign": "left",
			"verticalAlign": "top",
			"containerId": "config-rect",
			"originalText": "多配置源统一管理\n\n• Nacos配置中心\n• APS Dubbo服务\n• 配置合并策略\n• 编程式构建",
			"lineHeight": 1.25,
			"baseline": 93
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "aop-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 260,
			"y": 400,
			"strokeColor": "#7048e8",
			"backgroundColor": "#e5dbff",
			"width": 180,
			"height": 140,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "aop-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "aop-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 275,
			"y": 420,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 150,
			"height": 100,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 14,
			"fontFamily": 1,
			"text": "统一AOP路由机制\n\n• ServiceContext获取\n• HintManager路由\n• 融担号分库算法\n• 业务代码零侵入",
			"textAlign": "left",
			"verticalAlign": "top",
			"containerId": "aop-rect",
			"originalText": "统一AOP路由机制\n\n• ServiceContext获取\n• HintManager路由\n• 融担号分库算法\n• 业务代码零侵入",
			"lineHeight": 1.25,
			"baseline": 93
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "validation-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 470,
			"y": 400,
			"strokeColor": "#7048e8",
			"backgroundColor": "#e5dbff",
			"width": 180,
			"height": 140,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "validation-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "validation-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 485,
			"y": 420,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 150,
			"height": 100,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 14,
			"fontFamily": 1,
			"text": "功能对等性验证\n\n• 路由一致性测试\n• 性能对比测试\n• 事务一致性测试\n• 迁移验证机制",
			"textAlign": "left",
			"verticalAlign": "top",
			"containerId": "validation-rect",
			"originalText": "功能对等性验证\n\n• 路由一致性测试\n• 性能对比测试\n• 事务一致性测试\n• 迁移验证机制",
			"lineHeight": 1.25,
			"baseline": 93
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "benefits-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 680,
			"y": 400,
			"strokeColor": "#7048e8",
			"backgroundColor": "#e5dbff",
			"width": 180,
			"height": 140,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "benefits-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "benefits-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 695,
			"y": 420,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 150,
			"height": 100,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 14,
			"fontFamily": 1,
			"text": "架构统一收益\n\n• 技术栈标准化\n• 分表能力准备\n• 企业级特性\n• 平滑过渡保障",
			"textAlign": "left",
			"verticalAlign": "top",
			"containerId": "benefits-rect",
			"originalText": "架构统一收益\n\n• 技术栈标准化\n• 分表能力准备\n• 企业级特性\n• 平滑过渡保障",
			"lineHeight": 1.25,
			"baseline": 93
		},
		{
			"type": "arrow",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "flow-arrow1",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 230,
			"y": 470,
			"strokeColor": "#7048e8",
			"backgroundColor": "transparent",
			"width": 30,
			"height": 0,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 2
			},
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"startBinding": null,
			"endBinding": null,
			"lastCommittedPoint": null,
			"startArrowhead": null,
			"endArrowhead": "arrow",
			"points": [
				[0, 0],
				[30, 0]
			]
		},
		{
			"type": "arrow",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "flow-arrow2",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 440,
			"y": 470,
			"strokeColor": "#7048e8",
			"backgroundColor": "transparent",
			"width": 30,
			"height": 0,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 2
			},
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"startBinding": null,
			"endBinding": null,
			"lastCommittedPoint": null,
			"startArrowhead": null,
			"endArrowhead": "arrow",
			"points": [
				[0, 0],
				[30, 0]
			]
		},
		{
			"type": "arrow",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "flow-arrow3",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 650,
			"y": 470,
			"strokeColor": "#7048e8",
			"backgroundColor": "transparent",
			"width": 30,
			"height": 0,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 2
			},
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"startBinding": null,
			"endBinding": null,
			"lastCommittedPoint": null,
			"startArrowhead": null,
			"endArrowhead": "arrow",
			"points": [
				[0, 0],
				[30, 0]
			]
		},
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "phase2-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "dashed",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 200,
			"y": 580,
			"strokeColor": "#2f9e44",
			"backgroundColor": "#c3fae8",
			"width": 400,
			"height": 80,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "phase2-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "phase2-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 220,
			"y": 600,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 360,
			"height": 40,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 16,
			"fontFamily": 1,
			"text": "第二阶段：库内分表改造\n「在第一阶段基础上增加分表能力」",
			"textAlign": "center",
			"verticalAlign": "middle",
			"containerId": "phase2-rect",
			"originalText": "第二阶段：库内分表改造\n「在第一阶段基础上增加分表能力」",
			"lineHeight": 1.25,
			"baseline": 33
		},
		{
			"type": "arrow",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "phase-arrow",
			"fillStyle": "solid",
			"strokeWidth": 3,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 400,
			"y": 540,
			"strokeColor": "#2f9e44",
			"backgroundColor": "transparent",
			"width": 0,
			"height": 40,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 2
			},
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"startBinding": null,
			"endBinding": null,
			"lastCommittedPoint": null,
			"startArrowhead": null,
			"endArrowhead": "arrow",
			"points": [
				[0, 0],
				[0, 40]
			]
		}
	],
	"appState": {
		"theme": "light",
		"viewBackgroundColor": "#ffffff",
		"currentItemStrokeColor": "#1e1e1e",
		"currentItemBackgroundColor": "transparent",
		"currentItemFillStyle": "solid",
		"currentItemStrokeWidth": 2,
		"currentItemStrokeStyle": "solid",
		"currentItemRoughness": 1,
		"currentItemOpacity": 100,
		"currentItemFontFamily": 1,
		"currentItemFontSize": 20,
		"currentItemTextAlign": "left",
		"currentItemStartArrowhead": null,
		"currentItemEndArrowhead": "arrow",
		"scrollX": 0,
		"scrollY": 0,
		"zoom": {
			"value": 1
		},
		"currentItemRoundness": "round",
		"gridSize": null,
		"gridColor": {
			"Bold": "#C9C9C9FF",
			"Regular": "#EDEDEDFF"
		},
		"currentStrokeOptions": null,
		"previousGridSize": null,
		"frameRendering": {
			"enabled": true,
			"clip": true,
			"name": true,
			"outline": true
		}
	},
	"files": {}
}
```
%%
