---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title-rect",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 200,
      "y": 50,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#a5d8ff",
      "width": 500,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 220,
      "y": 70,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 460,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🔄 HintManager实现一条DAO写新老表流程图",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔄 HintManager实现一条DAO写新老表流程图",
      "lineHeight": 1.25,
      "baseline": 18
    },
    {
      "type": "ellipse",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "start-node",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 150,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#c3fae8",
      "width": 200,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "start-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 175,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "📥 开始\n业务层调用DAO方法",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📥 开始\n业务层调用DAO方法",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "hint-setup",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 280,
      "strokeColor": "#7048e8",
      "backgroundColor": "#e5dbff",
      "width": 300,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "hint-setup-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 370,
      "y": 305,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 260,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🔧 HintManager设置路由提示\nHintManager.setDatabaseShardingValue\n「key, shardingValue」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔧 HintManager设置路由提示\nHintManager.setDatabaseShardingValue\n「key, shardingValue」",
      "lineHeight": 1.25,
      "baseline": 43
    },
    {
      "type": "diamond",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "business-decision",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 425,
      "y": 430,
      "strokeColor": "#f08c00",
      "backgroundColor": "#ffd43b",
      "width": 150,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "business-decision-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 445,
      "y": 470,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 110,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🤔 业务逻辑判断\n写入新表？",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🤔 业务逻辑判断\n写入新表？",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "new-table-process",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 150,
      "y": 600,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#c3fae8",
      "width": 250,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "new-table-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 170,
      "y": 630,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 210,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🆕 写入新表逻辑\n• 设置新表路由提示\n• 执行INSERT操作\n• 记录操作日志",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🆕 写入新表逻辑\n• 设置新表路由提示\n• 执行INSERT操作\n• 记录操作日志",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "old-table-process",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 600,
      "y": 600,
      "strokeColor": "#c2410c",
      "backgroundColor": "#fed7aa",
      "width": 250,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "old-table-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 620,
      "y": 630,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 210,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📜 写入老表逻辑\n• 设置老表路由提示\n• 执行INSERT操作\n• 兼容性处理",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📜 写入老表逻辑\n• 设置老表路由提示\n• 执行INSERT操作\n• 兼容性处理",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "cleanup-process",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 780,
      "strokeColor": "#9333ea",
      "backgroundColor": "#ddd6fe",
      "width": 300,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "cleanup-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 370,
      "y": 805,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 260,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🧹 资源清理\nHintManager.clear「」\n清除所有路由提示",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🧹 资源清理\nHintManager.clear「」\n清除所有路由提示",
      "lineHeight": 1.25,
      "baseline": 43
    },
    {
      "type": "ellipse",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "end-node",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 930,
      "strokeColor": "#e03131",
      "backgroundColor": "#ffc9c9",
      "width": 200,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "end-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 955,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "✅ 结束\n返回操作结果",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "✅ 结束\n返回操作结果",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow1",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 230,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 50]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow2",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 380,
      "strokeColor": "#7048e8",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 50]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow3",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 425,
      "y": 490,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": -150,
      "height": 110,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [-150, 110]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow4",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 575,
      "y": 490,
      "strokeColor": "#c2410c",
      "backgroundColor": "transparent",
      "width": 150,
      "height": 110,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [150, 110]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow5",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 275,
      "y": 720,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": 75,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [75, 60]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow6",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 725,
      "y": 720,
      "strokeColor": "#c2410c",
      "backgroundColor": "transparent",
      "width": -75,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [-75, 60]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow7",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 880,
      "strokeColor": "#9333ea",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 50]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "yes-label",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 320,
      "y": 540,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": 40,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "是",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "是",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "no-label",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 640,
      "y": 540,
      "strokeColor": "#c2410c",
      "backgroundColor": "transparent",
      "width": 40,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "否",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "否",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "code-example",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "dashed",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 1050,
      "strokeColor": "#6b7280",
      "backgroundColor": "#f9fafb",
      "width": 800,
      "height": 150,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "code-example-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 1070,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 760,
      "height": 110,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "💻 核心代码示例:\n\npublic void insertOrder「Order order」 {\n    try {\n        // 设置路由提示\n        HintManager.setDatabaseShardingValue「『order_db』, order.getShardingKey「」」;\n        \n        // 业务逻辑判断\n        if 「shouldWriteToNewTable「order」」 {\n            // 写入新表逻辑\n            orderMapper.insertToNewTable「order」;\n        } else {\n            // 写入老表逻辑  \n            orderMapper.insertToOldTable「order」;\n        }\n    } finally {\n        // 清理资源\n        HintManager.clear「」;\n    }\n}",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "💻 核心代码示例:\n\npublic void insertOrder「Order order」 {\n    try {\n        // 设置路由提示\n        HintManager.setDatabaseShardingValue「『order_db』, order.getShardingKey「」」;\n        \n        // 业务逻辑判断\n        if 「shouldWriteToNewTable「order」」 {\n            // 写入新表逻辑\n            orderMapper.insertToNewTable「order」;\n        } else {\n            // 写入老表逻辑  \n            orderMapper.insertToOldTable「order」;\n        }\n    } finally {\n        // 清理资源\n        HintManager.clear「」;\n    }\n}",
      "lineHeight": 1.25,
      "baseline": 103
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
