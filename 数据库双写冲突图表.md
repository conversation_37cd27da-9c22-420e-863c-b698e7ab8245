---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "id": "title",
      "type": "text",
      "x": 375,
      "y": -100,
      "width": 850,
      "height": 33.6,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontFamily": 5,
      "fontSize": 28,
      "textAlign": "center",
      "verticalAlign": "middle",
      "text": "Scenario 1: UPDATE Conflict for Pre-Migration Data",
      "lineHeight": 1.2
    },
    {
      "id": "timeline_axis",
      "type": "arrow",
      "x": -100,
      "y": 0,
      "width": 1600,
      "height": 0,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "hachure",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 0,
      "opacity": 100,
      "points": [
        [
          0,
          0
        ],
        [
          1600,
          0
        ]
      ],
      "startArrowhead": null,
      "endArrowhead": "arrow"
    },
    {
      "id": "t0_marker",
      "type": "text",
      "x": 50,
      "y": -25,
      "width": 40,
      "height": 24,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "verticalAlign": "middle",
      "text": "T0"
    },
    {
      "id": "t1_marker",
      "type": "text",
      "x": 425,
      "y": -25,
      "width": 40,
      "height": 24,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "verticalAlign": "middle",
      "text": "T1"
    },
    {
      "id": "t2_marker",
      "type": "text",
      "x": 650,
      "y": -25,
      "width": 40,
      "height": 24,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "verticalAlign": "middle",
      "text": "T2"
    },
    {
      "id": "t3_marker",
      "type": "text",
      "x": 1000,
      "y": -25,
      "width": 40,
      "height": 24,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "verticalAlign": "middle",
      "text": "T3"
    },
    {
      "id": "t4_marker",
      "type": "text",
      "x": 1375,
      "y": -25,
      "width": 40,
      "height": 24,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "hachure",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "verticalAlign": "middle",
      "text": "T4"
    },
    {
      "id": "old_db_icon_t0",
      "type": "ellipse",
      "x": 50,
      "y": 100,
      "width": 100,
      "height": 40,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "#a5d8ff",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "old_db_body_t0",
      "type": "rectangle",
      "x": 50,
      "y": 120,
      "width": 100,
      "height": 80,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "#a5d8ff",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "roundness": {
        "type": 3
      }
    },
    {
      "id": "old_db_label_t0",
      "type": "text",
      "x": 5,
      "y": 210,
      "width": 190,
      "height": 24,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "text": "Old Table 「Production」"
    },
    {
      "id": "historical_data_box",
      "type": "rectangle",
      "x": -20,
      "y": 250,
      "width": 240,
      "height": 80,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#ffffff",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "dashed",
      "roughness": 1,
      "opacity": 100,
      "roundness": {
        "type": 3
      }
    },
    {
      "id": "historical_data_text",
      "type": "text",
      "x": -10,
      "y": 260,
      "width": 220,
      "height": 60,
      "strokeColor": "#1e1e1e",
      "fontFamily": 5,
      "fontSize": 16,
      "textAlign": "center",
      "verticalAlign": "middle",
      "text": "Historical Data\nOrder ID: 12345\nStatus: PENDING"
    },
    {
      "id": "new_db_icon_t0",
      "type": "ellipse",
      "x": 50,
      "y": 400,
      "width": 100,
      "height": 40,
      "angle": 0,
      "strokeColor": "#e8590c",
      "backgroundColor": "#ffd8a8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "new_db_body_t0",
      "type": "rectangle",
      "x": 50,
      "y": 420,
      "width": 100,
      "height": 80,
      "angle": 0,
      "strokeColor": "#e8590c",
      "backgroundColor": "#ffd8a8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "roundness": {
        "type": 3
      }
    },
    {
      "id": "new_db_label_t0",
      "type": "text",
      "x": 10,
      "y": 510,
      "width": 180,
      "height": 24,
      "angle": 0,
      "strokeColor": "#e8590c",
      "backgroundColor": "transparent",
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "text": "New Table 「Sharded」"
    },
    {
      "id": "milestone_t1",
      "type": "diamond",
      "x": 350,
      "y": 50,
      "width": 200,
      "height": 60,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#f1f3f5",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "milestone_text_t1",
      "type": "text",
      "x": 360,
      "y": 68,
      "width": 180,
      "height": 24,
      "strokeColor": "#1e1e1e",
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "text": "Dual-Write Strategy Starts"
    },
    {
      "id": "update_request_box",
      "type": "rectangle",
      "x": 575,
      "y": 280,
      "width": 200,
      "height": 40,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#f8f9fa",
      "fillStyle": "solid",
      "strokeWidth": 1
    },
    {
      "id": "update_request_text",
      "type": "text",
      "x": 585,
      "y": 288,
      "width": 180,
      "height": 24,
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "center",
      "text": "Business UPDATE Request"
    },
    {
      "id": "arrow_to_dbs",
      "type": "arrow",
      "x": 775,
      "y": 300,
      "width": 100,
      "height": 0,
      "points": [
        [
          0,
          0
        ],
        [
          100,
          0
        ]
      ],
      "endArrowhead": "arrow"
    },
    {
      "id": "arrow_to_old_db_t3",
      "type": "arrow",
      "x": 875,
      "y": 300,
      "width": 100,
      "height": 140,
      "points": [
        [
          0,
          0
        ],
        [
          100,
          -140
        ]
      ],
      "endArrowhead": "arrow",
      "strokeColor": "#2f9e44"
    },
    {
      "id": "arrow_to_new_db_t3",
      "type": "arrow",
      "x": 875,
      "y": 300,
      "width": 100,
      "height": 160,
      "points": [
        [
          0,
          0
        ],
        [
          100,
          160
        ]
      ],
      "endArrowhead": "arrow",
      "strokeColor": "#c92a2a"
    },
    {
      "id": "success_check",
      "type": "line",
      "x": 985,
      "y": 145,
      "width": 30,
      "height": 30,
      "strokeColor": "#2f9e44",
      "strokeWidth": 4,
      "points": [
        [
          0,
          15
        ],
        [
          10,
          25
        ],
        [
          30,
          0
        ]
      ]
    },
    {
      "id": "success_text",
      "type": "text",
      "x": 1025,
      "y": 120,
      "width": 220,
      "height": 48,
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "left",
      "text": "UPDATE SUCCESS\nStatus: COMPLETED"
    },
    {
      "id": "failure_x1",
      "type": "line",
      "x": 985,
      "y": 445,
      "width": 20,
      "height": 20,
      "strokeColor": "#c92a2a",
      "strokeWidth": 4,
      "points": [
        [
          0,
          0
        ],
        [
          20,
          20
        ]
      ]
    },
    {
      "id": "failure_x2",
      "type": "line",
      "x": 985,
      "y": 445,
      "width": 20,
      "height": 20,
      "strokeColor": "#c92a2a",
      "strokeWidth": 4,
      "points": [
        [
          20,
          0
        ],
        [
          0,
          20
        ]
      ]
    },
    {
      "id": "failure_text",
      "type": "text",
      "x": 1025,
      "y": 420,
      "width": 220,
      "height": 48,
      "fontFamily": 5,
      "fontSize": 20,
      "textAlign": "left",
      "text": "UPDATE FAILED\nAffected Rows: 0"
    },
    {
      "id": "old_db_icon_t4",
      "type": "ellipse",
      "x": 1350,
      "y": 100,
      "width": 100,
      "height": 40,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "#a5d8ff",
      "fillStyle": "solid",
      "strokeWidth": 2
    },
    {
      "id": "old_db_body_t4",
      "type": "rectangle",
      "x": 1350,
      "y": 120,
      "width": 100,
      "height": 80,
      "strokeColor": "#1971c2",
      "backgroundColor": "#a5d8ff",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "roundness": {
        "type": 3
      }
    },
    {
      "id": "final_data_box_old",
      "type": "rectangle",
      "x": 1280,
      "y": 210,
      "width": 240,
      "height": 80,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#ebfbee",
      "fillStyle": "solid",
      "strokeWidth": 2
    },
    {
      "id": "final_data_text_old",
      "type": "text",
      "x": 1290,
      "y": 220,
      "width": 220,
      "height": 60,
      "fontFamily": 5,
      "fontSize": 16,
      "textAlign": "center",
      "text": "Final State\nOrder ID: 12345\nStatus: COMPLETED"
    },
    {
      "id": "new_db_icon_t4",
      "type": "ellipse",
      "x": 1350,
      "y": 400,
      "width": 100,
      "height": 40,
      "angle": 0,
      "strokeColor": "#e8590c",
      "backgroundColor": "#ffd8a8",
      "fillStyle": "solid",
      "strokeWidth": 2
    },
    {
      "id": "new_db_body_t4",
      "type": "rectangle",
      "x": 1350,
      "y": 420,
      "width": 100,
      "height": 80,
      "strokeColor": "#e8590c",
      "backgroundColor": "#ffd8a8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "roundness": {
        "type": 3
      }
    },
    {
      "id": "final_data_box_new",
      "type": "rectangle",
      "x": 1280,
      "y": 510,
      "width": 240,
      "height": 60,
      "strokeColor": "#c92a2a",
      "backgroundColor": "#fff5f5",
      "fillStyle": "solid",
      "strokeWidth": 2
    },
    {
      "id": "final_data_text_new",
      "type": "text",
      "x": 1290,
      "y": 520,
      "width": 220,
      "height": 40,
      "fontFamily": 5,
      "fontSize": 16,
      "textAlign": "center",
      "text": "Final State\nData does not exist"
    },
    {
      "id": "warning_symbol_triangle",
      "type": "diamond",
      "x": 1385,
      "y": 320,
      "width": 30,
      "height": 30,
      "strokeColor": "#fab005",
      "backgroundColor": "#ffec99",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "roughness": 0,
      "boundElements": [
        {
          "id": "warning_symbol_text",
          "type": "arrow"
        }
      ]
    },
    {
      "id": "warning_symbol_exclamation",
      "type": "text",
      "x": 1395,
      "y": 323,
      "width": 10,
      "height": 24,
      "fontFamily": 1,
      "fontSize": 20,
      "textAlign": "center",
      "text": "!",
      "strokeColor": "#fab005"
    },
    {
      "id": "warning_text",
      "type": "text",
      "x": 1290,
      "y": 360,
      "width": 220,
      "height": 29,
      "fontFamily": 5,
      "fontSize": 24,
      "textAlign": "center",
      "text": "Data Inconsistency",
      "strokeColor": "#c92a2a"
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%