---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title-layer",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 200,
      "y": 50,
      "strokeColor": "#e03131",
      "backgroundColor": "#ffc9c9",
      "width": 400,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 220,
      "y": 75,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 360,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 24,
      "fontFamily": 5,
      "text": "🔥 热数据索引机制架构层级图",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔥 热数据索引机制架构层级图",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "app-layer",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 180,
      "strokeColor": "#1971c2",
      "backgroundColor": "#d0ebff",
      "width": 600,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "app-layer-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 200,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 560,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🖥️ 应用层 - 业务查询入口「WHERE user_id = 『U123』」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🖥️ 应用层 - 业务查询入口「WHERE user_id = 『U123』」",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "decision-layer",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 300,
      "strokeColor": "#f08c00",
      "backgroundColor": "#ffd43b",
      "width": 600,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "decision-layer-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 320,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 560,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🤔 路由决策层 - 检测分片键并选择查询策略",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🤔 路由决策层 - 检测分片键并选择查询策略",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "hot-index-layer",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 420,
      "strokeColor": "#7048e8",
      "backgroundColor": "#e5dbff",
      "width": 600,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "hot-index-layer-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 440,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 560,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🔥 热索引查询层 - 两阶段查询机制",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔥 热索引查询层 - 两阶段查询机制",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "phase1-box",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 540,
      "strokeColor": "#7048e8",
      "backgroundColor": "#f8f0ff",
      "width": 250,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "phase1-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 140,
      "y": 560,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 210,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🔍 第一阶段\nSELECT id FROM t_order_index\nWHERE user_id = 『U123』",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔍 第一阶段\nSELECT id FROM t_order_index\nWHERE user_id = 『U123』",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "phase2-box",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 430,
      "y": 540,
      "strokeColor": "#7048e8",
      "backgroundColor": "#f8f0ff",
      "width": 250,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "phase2-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 450,
      "y": 560,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 210,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🎯 第二阶段\nSELECT * FROM t_order\nWHERE id IN 『order1,order2』",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 第二阶段\nSELECT * FROM t_order\nWHERE id IN 『order1,order2』",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "storage-layer",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 680,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#c3fae8",
      "width": 600,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "storage-layer-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 700,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 560,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "📊 数据存储层 - 索引表 + 分表集群",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📊 数据存储层 - 索引表 + 分表集群",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "index-table-box",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 800,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#f0fdf4",
      "width": 250,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "index-table-box-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 140,
      "y": 820,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 210,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📋 热数据索引表\nt_order_index\n\n⏰ 只保留14天热数据\n🗑️ 自动清理过期数据",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📋 热数据索引表\nt_order_index\n\n⏰ 只保留14天热数据\n🗑️ 自动清理过期数据",
      "lineHeight": 1.25,
      "baseline": 73
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "shard-tables-box",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 430,
      "y": 800,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#f0fdf4",
      "width": 250,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "shard-tables-box-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 450,
      "y": 820,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 210,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📊 分表集群\nt_order_0 ~ t_order_127\n\n🎯 128张物理表\n⚡ 精确路由查询",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📊 分表集群\nt_order_0 ~ t_order_127\n\n🎯 128张物理表\n⚡ 精确路由查询",
      "lineHeight": 1.25,
      "baseline": 73
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "performance-box",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 750,
      "y": 400,
      "strokeColor": "#f08c00",
      "backgroundColor": "#fff4e6",
      "width": 200,
      "height": 200,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "performance-box-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 770,
      "y": 420,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 160,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📊 性能对比\n\n传统广播查询:\n🐌 128次数据库查询\n⏱️ 响应时间: 1-5秒\n💾 资源消耗: 极高\n\n热索引查询:\n⚡ 2-5次精确查询\n⏱️ 响应时间: 10-50ms\n💾 资源消耗: 很低\n\n🚀 性能提升: 20-500倍",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📊 性能对比\n\n传统广播查询:\n🐌 128次数据库查询\n⏱️ 响应时间: 1-5秒\n💾 资源消耗: 极高\n\n热索引查询:\n⚡ 2-5次精确查询\n⏱️ 响应时间: 10-50ms\n💾 资源消耗: 很低\n\n🚀 性能提升: 20-500倍",
      "lineHeight": 1.25,
      "baseline": 153
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "layer-arrow1",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 130,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 50]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "layer-arrow2",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 260,
      "strokeColor": "#f08c00",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "layer-arrow3",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 380,
      "strokeColor": "#7048e8",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "layer-arrow4",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 640,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
