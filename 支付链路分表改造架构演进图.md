---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# 支付链路分表改造架构演进图

本图展示了从现有DynamicDataSource架构到ShardingSphere-JDBC的完整迁移路径，包括两个阶段的演进过程和详细的数据迁移流程。

## 图表说明

### 现状架构
- 使用DynamicDataSource进行分库管理
- 单表数据量过大，存在性能瓶颈
- 基于counterGuaranteeNo进行分库路由

### 第一阶段：平滑迁移
- 将DynamicDataSource替换为ShardingSphere-JDBC
- 保持功能对等，只做分库不做分表
- 为后续分表改造奠定技术基础

### 第二阶段：分库分表
- 实现完整的分库+分表架构
- 基于loan_order_id进行分表
- 彻底解决性能问题

### 数据迁移流程
- 7步完整迁移流程
- 支持灰度发布和快速回滚
- 确保数据零丢失

### 核心分表清单
- 6张核心表的分表方案
- 包含数据量统计和分片键设计
- 标注需要字段改造的表

## Mermaid 架构图

```mermaid
graph TB
    subgraph "现状架构"
        A1[DynamicDataSource<br/>分库管理]
        A2[DB0: 单表数据过大<br/>DB1: 单表数据过大<br/>DB2: 单表数据过大<br/>DB3: 单表数据过大]
        A1 --> A2
        A3[性能瓶颈<br/>查询缓慢]
        A2 --> A3
    end

    subgraph "第一阶段架构"
        B1[ShardingSphere-JDBC<br/>分库管理]
        B2[DB0: 功能对等<br/>DB1: 功能对等<br/>DB2: 功能对等<br/>DB3: 功能对等]
        B1 --> B2
        B3[为分表做准备<br/>技术栈统一]
        B2 --> B3
    end

    subgraph "第二阶段架构"
        C1[ShardingSphere-JDBC<br/>分库+分表]
        C2[DB0: t_order_0, t_order_1...<br/>DB1: t_order_0, t_order_1...<br/>DB2: t_order_0, t_order_1...<br/>DB3: t_order_0, t_order_1...]
        C1 --> C2
        C3[性能问题彻底解决<br/>支撑业务增长]
        C2 --> C3
    end

    subgraph "数据迁移流程"
        D1[1. 准备阶段：创建分表结构]
        D2[2. 启动双写：新旧表同时写入]
        D3[3. 迁移历史：离线脚本迁移存量数据]
        D4[4. 数据校验：确保新旧数据一致性]
        D5[5. 切换读流量：读操作切换到新表]
        D6[6. 停止双写：只写新表]
        D7[7. 清理旧表：归档并删除旧表]

        D1 --> D2 --> D3 --> D4 --> D5 --> D6 --> D7

        D8[关键特性：<br/>• 灰度发布，风险可控<br/>• 支持快速回滚<br/>• 数据零丢失保障]
    end

    subgraph "核心分表清单"
        E1[1. biz_order 业务订单表<br/>总量: ~8.9亿 日增: ~660万<br/>分片键: loan_order_id]
        E2[2. trans_order 交易订单表<br/>总量: ~21.8亿 日增: ~1638万<br/>分片键: loan_order_id]
        E3[3. withhold_order_info 代扣订单<br/>总量: ~11.9亿 日增: ~918万<br/>分片键: loan_order_id]
        E4[4. pay_route_order 支付路由<br/>推断分表<br/>分片键: loan_order_id]
        E5[5. payment_order 支付主订单<br/>推断分表<br/>需要增加loan_order_id字段]
        E6[6. pay_divide_detail 分账明细<br/>总量: ~1.17亿 日增: ~45万<br/>需要增加loan_order_id字段]
    end

    A1 -.->|第一阶段| B1
    B1 -.->|第二阶段| C1

    style A3 fill:#ffcccc
    style C3 fill:#ccffcc
    style D8 fill:#fff2cc
```

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "current-arch",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 50,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#e7f5ff",
      "width": 280,
      "height": 200,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "current-title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 70,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 140,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "现状架构",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "现状架构",
      "lineHeight": 1.25,
      "baseline": 18
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "dynamic-ds",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 110,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#fff2cc",
      "width": 240,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "dynamic-ds-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 90,
      "y": 120,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 200,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "DynamicDataSource「分库」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "DynamicDataSource「分库」",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "db-boxes",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 170,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#f8f9fa",
      "width": 240,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "db-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 80,
      "y": 180,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 220,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "DB0, DB1, DB2, DB3...\n单表数据量过大「性能瓶颈」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "DB0, DB1, DB2, DB3...\n单表数据量过大「性能瓶颈」",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow1",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 150,
      "strokeColor": "#e03131",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 0,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [80, 0]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "phase1-label",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 360,
      "y": 120,
      "strokeColor": "#e03131",
      "backgroundColor": "transparent",
      "width": 60,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "第一阶段",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "第一阶段",
      "lineHeight": 1.25,
      "baseline": 13
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
