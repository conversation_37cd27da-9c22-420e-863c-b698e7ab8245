---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "start",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 50,
      "strokeColor": "#1e88e5",
      "backgroundColor": "#e3f2fd",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "start-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 70,
      "strokeColor": "#1565c0",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "1. Service业务层\n设置融担编号到ThreadLocal",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "1. Service业务层\n设置融担编号到ThreadLocal",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 110,
      "strokeColor": "#1565c0",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "dao-call",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 150,
      "strokeColor": "#43a047",
      "backgroundColor": "#e8f5e8",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "dao-call-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 170,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "2. 调用DAO层方法\ntransOrderMapper.insert「order」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "2. 调用DAO层方法\ntransOrderMapper.insert「order」",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow2",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 210,
      "strokeColor": "#43a047",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "aop-intercept",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 250,
      "strokeColor": "#9c27b0",
      "backgroundColor": "#f3e5f5",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "aop-intercept-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 270,
      "strokeColor": "#7b1fa2",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "3. AOP拦截器触发\nDynamicDataSourceParamsAdvice",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "3. AOP拦截器触发\nDynamicDataSourceParamsAdvice",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow3",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 310,
      "strokeColor": "#9c27b0",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "context-read",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 350,
      "strokeColor": "#ff9800",
      "backgroundColor": "#fff3e0",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "context-read-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 370,
      "strokeColor": "#f57c00",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "4. 读取上下文信息\n融担编号=666 + 读写属性=Master",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "4. 读取上下文信息\n融担编号=666 + 读写属性=Master",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow4",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 410,
      "strokeColor": "#ff9800",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "context-push",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 450,
      "strokeColor": "#795548",
      "backgroundColor": "#efebe9",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "context-push-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 470,
      "strokeColor": "#5d4037",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "5. 路由信息压栈\nContextHolder.pushMethodInfo",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "5. 路由信息压栈\nContextHolder.pushMethodInfo",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow5",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 510,
      "strokeColor": "#795548",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "mybatis-request",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 550,
      "strokeColor": "#607d8b",
      "backgroundColor": "#eceff1",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "mybatis-request-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 570,
      "strokeColor": "#455a64",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "6. MyBatis请求数据源\nDynamicDataSource.determineCurrentLookupKey",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "6. MyBatis请求数据源\nDynamicDataSource.determineCurrentLookupKey",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow6",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 610,
      "strokeColor": "#607d8b",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "router-decision",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 650,
      "strokeColor": "#8e24aa",
      "backgroundColor": "#f8bbd9",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "router-decision-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 670,
      "strokeColor": "#6a1b9a",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "7. 路由决策执行\nDefaultDataSourceRouter.selectCurrentDataSourceKey",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "7. 路由决策执行\nDefaultDataSourceRouter.selectCurrentDataSourceKey",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow7",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 710,
      "strokeColor": "#8e24aa",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "route-key",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 750,
      "strokeColor": "#d32f2f",
      "backgroundColor": "#ffebee",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "route-key-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 770,
      "strokeColor": "#c62828",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "8. 生成最终路由Key\n『666_master』",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "8. 生成最终路由Key\n『666_master』",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 810,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "get-connection",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 850,
      "strokeColor": "#388e3c",
      "backgroundColor": "#c8e6c9",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "get-connection-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 870,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "9. 获取物理数据库连接\ntargetDataSources.get『666_master』",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "9. 获取物理数据库连接\ntargetDataSources.get『666_master』",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow9",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 910,
      "strokeColor": "#388e3c",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sql-execute",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 950,
      "strokeColor": "#ff5722",
      "backgroundColor": "#ffccbc",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sql-execute-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 970,
      "strokeColor": "#d84315",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "10. SQL在目标库执行\nINSERT INTO t_trans_order...",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "10. SQL在目标库执行\nINSERT INTO t_trans_order...",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow10",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 1010,
      "strokeColor": "#ff5722",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "cleanup",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 1050,
      "strokeColor": "#9e9e9e",
      "backgroundColor": "#f5f5f5",
      "width": 200,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "cleanup-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 1070,
      "strokeColor": "#616161",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "11. 上下文清理\nContextHolder.popMethodInfo",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "11. 上下文清理\nContextHolder.popMethodInfo",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "side-note1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 650,
      "y": 280,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 200,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "💡 关键点:\nAOP拦截所有DAO方法调用\n从ThreadLocal获取融担编号\n从配置读取读写属性",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "💡 关键点:\nAOP拦截所有DAO方法调用\n从ThreadLocal获取融担编号\n从配置读取读写属性",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "side-note2",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 650,
      "y": 680,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 200,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "🎯 路由规则:\n写操作 → 666_master\n读操作 → 666_slave_0/1\n负载均衡选择从库",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "🎯 路由规则:\n写操作 → 666_master\n读操作 → 666_slave_0/1\n负载均衡选择从库",
      "lineHeight": 1.25,
      "baseline": 11
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
