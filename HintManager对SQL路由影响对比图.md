---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title-rect",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 150,
      "y": 50,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#a5d8ff",
      "width": 600,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 170,
      "y": 70,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 560,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🎯 HintManager对SQL路由影响对比图",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 HintManager对SQL路由影响对比图",
      "lineHeight": 1.25,
      "baseline": 18
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sql-source",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 300,
      "y": 150,
      "strokeColor": "#7048e8",
      "backgroundColor": "#e5dbff",
      "width": 300,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sql-source-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 320,
      "y": 170,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 260,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📝 相同的SQL语句\nINSERT INTO t_order 「...」 VALUES 「...」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📝 相同的SQL语句\nINSERT INTO t_order 「...」 VALUES 「...」",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-section",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 280,
      "strokeColor": "#c2410c",
      "backgroundColor": "#fed7aa",
      "width": 350,
      "height": 500,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 300,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 310,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "🔄 默认分表逻辑「无Hint」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔄 默认分表逻辑「无Hint」",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-section",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 280,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#c3fae8",
      "width": 350,
      "height": 500,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 520,
      "y": 300,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 310,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "🎯 Hint强制路由「有Hint」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 Hint强制路由「有Hint」",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-step1",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 350,
      "strokeColor": "#c2410c",
      "backgroundColor": "#fff7ed",
      "width": 310,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-step1-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 90,
      "y": 365,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 270,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "1️⃣ SQL解析\n解析INSERT语句，提取分片键值",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "1️⃣ SQL解析\n解析INSERT语句，提取分片键值",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-step2",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 430,
      "strokeColor": "#c2410c",
      "backgroundColor": "#fff7ed",
      "width": 310,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-step2-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 90,
      "y": 445,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 270,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "2️⃣ 分片算法计算\nuser_id % 128 = 2",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "2️⃣ 分片算法计算\nuser_id % 128 = 2",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-step3",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 510,
      "strokeColor": "#c2410c",
      "backgroundColor": "#fff7ed",
      "width": 310,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-step3-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 90,
      "y": 525,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 270,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "3️⃣ 路由决策\n根据算法结果选择目标表",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "3️⃣ 路由决策\n根据算法结果选择目标表",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-result",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 590,
      "strokeColor": "#dc2626",
      "backgroundColor": "#fecaca",
      "width": 310,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-result-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 90,
      "y": 610,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 270,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📊 最终执行\nINSERT INTO t_order_2 「...」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📊 最终执行\nINSERT INTO t_order_2 「...」",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-step1",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 520,
      "y": 350,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#f0fdf4",
      "width": 310,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-step1-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 540,
      "y": 365,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 270,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "1️⃣ HintManager设置\nsetDatabaseShardingValue「『5』」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "1️⃣ HintManager设置\nsetDatabaseShardingValue「『5』」",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-step2",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 520,
      "y": 430,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#f0fdf4",
      "width": 310,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-step2-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 540,
      "y": 445,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 270,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "2️⃣ Hint检测\nShardingSphere检测到Hint存在",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "2️⃣ Hint检测\nShardingSphere检测到Hint存在",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-step3",
      "fillStyle": "solid",
      "strokeWidth": 1,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 520,
      "y": 510,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#f0fdf4",
      "width": 310,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-step3-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 540,
      "y": 525,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 270,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "3️⃣ 跳过分片算法\n直接使用Hint指定的值",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "3️⃣ 跳过分片算法\n直接使用Hint指定的值",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-result",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 520,
      "y": 590,
      "strokeColor": "#16a34a",
      "backgroundColor": "#bbf7d0",
      "width": 310,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-result-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 540,
      "y": 610,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 270,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🎯 最终执行\nINSERT INTO t_order_5 「...」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 最终执行\nINSERT INTO t_order_5 「...」",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "left-arrow",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 230,
      "strokeColor": "#c2410c",
      "backgroundColor": "transparent",
      "width": -175,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [-175, 50]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "right-arrow",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 230,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": 175,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [175, 50]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "core-mechanism",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "dashed",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 200,
      "y": 820,
      "strokeColor": "#7048e8",
      "backgroundColor": "#f3f4f6",
      "width": 500,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "core-mechanism-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 220,
      "y": 840,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 460,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🔑 核心机制说明:\n\n• HintManager通过ThreadLocal存储路由提示\n• ShardingSphere优先检查Hint，存在时跳过分片算法\n• 相同SQL + 不同Hint = 不同目标表\n• 实现了对分表逻辑的完全控制和规避",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔑 核心机制说明:\n\n• HintManager通过ThreadLocal存储路由提示\n• ShardingSphere优先检查Hint，存在时跳过分片算法\n• 相同SQL + 不同Hint = 不同目标表\n• 实现了对分表逻辑的完全控制和规避",
      "lineHeight": 1.25,
      "baseline": 73
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "code-example",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 980,
      "strokeColor": "#6b7280",
      "backgroundColor": "#f9fafb",
      "width": 800,
      "height": 180,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "code-example-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 1000,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 760,
      "height": 140,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "💻 代码示例对比:\n\n// 默认分表逻辑「左侧」\nString sql = 『INSERT INTO t_order 「user_id, order_no」 VALUES 「123, 『ORDER001』」』;\n// 执行结果: t_order_2 「根据 123 % 128 = 2」\n\n// Hint强制路由「右侧」  \nHintManager.setDatabaseShardingValue「『t_order』, 5」;\nString sql = 『INSERT INTO t_order 「user_id, order_no」 VALUES 「123, 『ORDER001』」』;\n// 执行结果: t_order_5 「强制路由到指定表」\nHintManager.clear「」;\n\n🎯 关键点: 相同SQL语句，通过Hint实现不同表的写入，完全规避了默认分片逻辑！",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "💻 代码示例对比:\n\n// 默认分表逻辑「左侧」\nString sql = 『INSERT INTO t_order 「user_id, order_no」 VALUES 「123, 『ORDER001』」』;\n// 执行结果: t_order_2 「根据 123 % 128 = 2」\n\n// Hint强制路由「右侧」  \nHintManager.setDatabaseShardingValue「『t_order』, 5」;\nString sql = 『INSERT INTO t_order 「user_id, order_no」 VALUES 「123, 『ORDER001』」』;\n// 执行结果: t_order_5 「强制路由到指定表」\nHintManager.clear「」;\n\n🎯 关键点: 相同SQL语句，通过Hint实现不同表的写入，完全规避了默认分片逻辑！",
      "lineHeight": 1.25,
      "baseline": 133
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "vs-label",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 520,
      "strokeColor": "#7048e8",
      "backgroundColor": "transparent",
      "width": 60,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 24,
      "fontFamily": 5,
      "text": "VS",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "VS",
      "lineHeight": 1.25,
      "baseline": 33
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
