---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 30,
      "strokeColor": "#1e88e5",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 24,
      "fontFamily": 5,
      "text": "⏰ 中间索引表时间窗口管理",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⏰ 中间索引表时间窗口管理",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "subtitle",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 70,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 400,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "用于解决不带分片键查询的索引表数据生命周期管理",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "用于解决不带分片键查询的索引表数据生命周期管理",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "time-window",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 150,
      "y": 150,
      "strokeColor": "#333333",
      "backgroundColor": "#f5f5f5",
      "width": 700,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "expired-zone",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 150,
      "y": 150,
      "strokeColor": "#f44336",
      "backgroundColor": "#ffebee",
      "width": 200,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "expired-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 200,
      "y": 190,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "🗑️ 过期数据区\n超过保留期限\n待脚本删除",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🗑️ 过期数据区\n超过保留期限\n待脚本删除",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "active-zone",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 150,
      "strokeColor": "#4caf50",
      "backgroundColor": "#e8f5e8",
      "width": 300,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "active-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 450,
      "y": 190,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "📊 存量数据区\n日常查询使用\n索引定位分片",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📊 存量数据区\n日常查询使用\n索引定位分片",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "increment-zone",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 650,
      "y": 150,
      "strokeColor": "#2196f3",
      "backgroundColor": "#e3f2fd",
      "width": 200,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "increment-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 700,
      "y": 190,
      "strokeColor": "#1565c0",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "📈 增量数据区\n双写更新\n实时同步",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📈 增量数据区\n双写更新\n实时同步",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "cursor-line",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 140,
      "strokeColor": "#ff5722",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 140,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [0, 140]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "cursor-label",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 310,
      "y": 120,
      "strokeColor": "#ff5722",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "📍 删除游标分界线",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📍 删除游标分界线",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "script-box",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 320,
      "strokeColor": "#9c27b0",
      "backgroundColor": "#f3e5f5",
      "width": 150,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "script-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 345,
      "strokeColor": "#7b1fa2",
      "backgroundColor": "transparent",
      "width": 50,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "⚙️ 清理脚本\nCleanupScript\n定时执行",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⚙️ 清理脚本\nCleanupScript\n定时执行",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "script-arrow",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 200,
      "y": 350,
      "strokeColor": "#9c27b0",
      "backgroundColor": "transparent",
      "width": 80,
      "height": -80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [80, -80]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "script-action",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 220,
      "y": 300,
      "strokeColor": "#9c27b0",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "DELETE FROM index_table\nWHERE create_time < ?",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "DELETE FROM index_table\nWHERE create_time < ?",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "data-flow-box",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 750,
      "y": 320,
      "strokeColor": "#2196f3",
      "backgroundColor": "#e3f2fd",
      "width": 150,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "data-flow-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 800,
      "y": 345,
      "strokeColor": "#1565c0",
      "backgroundColor": "transparent",
      "width": 50,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📥 数据双写\nDualWrite\n实时同步",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📥 数据双写\nDualWrite\n实时同步",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "data-flow-arrow",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 750,
      "y": 350,
      "strokeColor": "#2196f3",
      "backgroundColor": "transparent",
      "width": -80,
      "height": -80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [-80, -80]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "data-flow-action",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 620,
      "y": 300,
      "strokeColor": "#2196f3",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "INSERT INTO index_table\n(order_id, shard_key, create_time)",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "INSERT INTO index_table\n(order_id, shard_key, create_time)",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "time-axis",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 150,
      "y": 290,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 700,
      "height": 0,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [700, 0]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "time-label1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 200,
      "y": 295,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "30天前",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "30天前",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "time-label2",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 340,
      "y": 295,
      "strokeColor": "#ff5722",
      "backgroundColor": "transparent",
      "width": 20,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "7天前",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "7天前",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "time-label3",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 295,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "昨天",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "昨天",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "time-label4",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 750,
      "y": 295,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "现在",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "现在",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "query-example",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 320,
      "strokeColor": "#4caf50",
      "backgroundColor": "#e8f5e8",
      "width": 200,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "query-example-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 450,
      "y": 345,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🔍 索引查询\nSELECT shard_key\nFROM index_table\nWHERE order_id=?",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔍 索引查询\nSELECT shard_key\nFROM index_table\nWHERE order_id=?",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "query-arrow",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 320,
      "strokeColor": "#4caf50",
      "backgroundColor": "transparent",
      "width": 0,
      "height": -50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, -50]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "summary",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 150,
      "y": 450,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 700,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "💡 核心机制:\n1. 🗑️ 过期数据区: 超过保留期限(如7天)的索引数据，由定时脚本清理删除\n2. 📊 存量数据区: 日常业务查询使用的有效索引数据，用于定位分片键\n3. 📈 增量数据区: 新产生的业务数据通过双写机制实时同步到索引表\n4. 📍 删除游标: 动态分界线，随时间推移不断右移，控制数据保留窗口大小",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "💡 核心机制:\n1. 🗑️ 过期数据区: 超过保留期限(如7天)的索引数据，由定时脚本清理删除\n2. 📊 存量数据区: 日常业务查询使用的有效索引数据，用于定位分片键\n3. 📈 增量数据区: 新产生的业务数据通过双写机制实时同步到索引表\n4. 📍 删除游标: 动态分界线，随时间推移不断右移，控制数据保留窗口大小",
      "lineHeight": 1.25,
      "baseline": 52
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
