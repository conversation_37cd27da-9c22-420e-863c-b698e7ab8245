---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "id": "title",
      "type": "text",
      "x": 200,
      "y": 50,
      "width": 600,
      "height": 35,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 28,
      "fontFamily": 5,
      "text": "Scenario 1: UPDATE Conflict for Pre-Migration Data",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "timeline-base",
      "type": "line",
      "x": 100,
      "y": 150,
      "width": 800,
      "height": 0,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [800, 0]]
    },
    {
      "id": "t0-marker",
      "type": "line",
      "x": 150,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t0-label",
      "type": "text",
      "x": 135,
      "y": 120,
      "width": 30,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "T0",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "t1-marker",
      "type": "line",
      "x": 300,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t1-label",
      "type": "text",
      "x": 285,
      "y": 120,
      "width": 30,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "T1",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "t2-marker",
      "type": "line",
      "x": 450,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t2-label",
      "type": "text",
      "x": 435,
      "y": 120,
      "width": 30,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "T2",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "t3-marker",
      "type": "line",
      "x": 600,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t3-label",
      "type": "text",
      "x": 585,
      "y": 120,
      "width": 30,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "T3",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "t4-marker",
      "type": "line",
      "x": 750,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t4-label",
      "type": "text",
      "x": 735,
      "y": 120,
      "width": 30,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "T4",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "old-db-icon",
      "type": "rectangle",
      "x": 100,
      "y": 200,
      "width": 80,
      "height": 60,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "#a5d8ff",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "old-db-label",
      "type": "text",
      "x": 110,
      "y": 220,
      "width": 60,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "Old DB",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "old-db-title",
      "type": "text",
      "x": 50,
      "y": 270,
      "width": 180,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "Old Table 「Production」",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "new-db-icon",
      "type": "rectangle",
      "x": 100,
      "y": 320,
      "width": 80,
      "height": 60,
      "angle": 0,
      "strokeColor": "#fd7e14",
      "backgroundColor": "#ffd8a8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "new-db-label",
      "type": "text",
      "x": 110,
      "y": 340,
      "width": 60,
      "height": 20,
      "angle": 0,
      "strokeColor": "#fd7e14",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "New DB",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "new-db-title",
      "type": "text",
      "x": 50,
      "y": 390,
      "width": 180,
      "height": 20,
      "angle": 0,
      "strokeColor": "#fd7e14",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "New Table 「Sharded」",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "historical-data-box",
      "type": "rectangle",
      "x": 250,
      "y": 200,
      "width": 200,
      "height": 60,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "#e7f5ff",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "historical-data-title",
      "type": "text",
      "x": 260,
      "y": 210,
      "width": 180,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "Historical Data",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "historical-data-content",
      "type": "text",
      "x": 260,
      "y": 235,
      "width": 180,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "Order ID: 12345, Status: PENDING",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "dual-write-milestone",
      "type": "rectangle",
      "x": 480,
      "y": 200,
      "width": 180,
      "height": 40,
      "angle": 0,
      "strokeColor": "#495057",
      "backgroundColor": "#f8f9fa",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "dual-write-text",
      "type": "text",
      "x": 490,
      "y": 210,
      "width": 160,
      "height": 20,
      "angle": 0,
      "strokeColor": "#495057",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "Dual-Write Strategy Starts",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "business-update-box",
      "type": "rectangle",
      "x": 680,
      "y": 180,
      "width": 160,
      "height": 40,
      "angle": 0,
      "strokeColor": "#495057",
      "backgroundColor": "#fff3cd",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "business-update-text",
      "type": "text",
      "x": 690,
      "y": 190,
      "width": 140,
      "height": 20,
      "angle": 0,
      "strokeColor": "#495057",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "Business UPDATE Request",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "update-arrow-to-old",
      "type": "arrow",
      "x": 760,
      "y": 220,
      "width": 0,
      "height": 80,
      "angle": 0,
      "strokeColor": "#51cf66",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 80]],
      "lastCommittedPoint": [0, 80],
      "startBinding": null,
      "endBinding": null,
      "startArrowhead": null,
      "endArrowhead": "arrow"
    },
    {
      "id": "update-arrow-to-new",
      "type": "arrow",
      "x": 760,
      "y": 220,
      "width": 0,
      "height": 200,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 200]],
      "lastCommittedPoint": [0, 200],
      "startBinding": null,
      "endBinding": null,
      "startArrowhead": null,
      "endArrowhead": "arrow"
    },
    {
      "id": "success-checkmark",
      "type": "text",
      "x": 770,
      "y": 290,
      "width": 20,
      "height": 20,
      "angle": 0,
      "strokeColor": "#51cf66",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "✓",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "success-text",
      "type": "text",
      "x": 800,
      "y": 280,
      "width": 200,
      "height": 40,
      "angle": 0,
      "strokeColor": "#51cf66",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "UPDATE SUCCESS\nStatus: COMPLETED",
      "textAlign": "left",
      "verticalAlign": "middle"
    },
    {
      "id": "failure-x",
      "type": "text",
      "x": 770,
      "y": 410,
      "width": 20,
      "height": 20,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "✗",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "failure-text",
      "type": "text",
      "x": 800,
      "y": 400,
      "width": 200,
      "height": 40,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "UPDATE FAILED\nAffected Rows: 0",
      "textAlign": "left",
      "verticalAlign": "middle"
    },
    {
      "id": "final-old-state",
      "type": "rectangle",
      "x": 250,
      "y": 480,
      "width": 200,
      "height": 60,
      "angle": 0,
      "strokeColor": "#51cf66",
      "backgroundColor": "#d3f9d8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "final-old-content",
      "type": "text",
      "x": 260,
      "y": 500,
      "width": 180,
      "height": 20,
      "angle": 0,
      "strokeColor": "#51cf66",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "Order ID: 12345, Status: COMPLETED",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "final-new-state",
      "type": "rectangle",
      "x": 250,
      "y": 560,
      "width": 200,
      "height": 60,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "#ffe0e0",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "final-new-content",
      "type": "text",
      "x": 260,
      "y": 580,
      "width": 180,
      "height": 20,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "No Data 「Empty」",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "inconsistency-warning",
      "type": "text",
      "x": 500,
      "y": 520,
      "width": 30,
      "height": 30,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 24,
      "fontFamily": 5,
      "text": "⚠",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "inconsistency-text",
      "type": "text",
      "x": 540,
      "y": 510,
      "width": 150,
      "height": 50,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "Data Inconsistency",
      "textAlign": "center",
      "verticalAlign": "middle"
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
