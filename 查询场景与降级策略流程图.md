---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title-rect",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 200,
      "y": 50,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "#a5d8ff",
      "width": 400,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 220,
      "y": 70,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 360,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🔍 查询场景与降级策略流程图",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔍 查询场景与降级策略流程图",
      "lineHeight": 1.25,
      "baseline": 18
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "query-start",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 150,
      "strokeColor": "#1971c2",
      "backgroundColor": "#d0ebff",
      "width": 200,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "query-start-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 370,
      "y": 170,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "📥 查询请求\n进入系统",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📥 查询请求\n进入系统",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "diamond",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sharding-key-check",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 375,
      "y": 280,
      "strokeColor": "#f08c00",
      "backgroundColor": "#ffd43b",
      "width": 150,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sharding-key-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 395,
      "y": 310,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 110,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🔑 是否包含\n分片键？",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔑 是否包含\n分片键？",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "direct-sharding",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 600,
      "y": 280,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#c3fae8",
      "width": 180,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "direct-sharding-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 620,
      "y": 300,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 140,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🎯 直接分表路由\nDIRECT_SHARDING\n⚡ 性能最优",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 直接分表路由\nDIRECT_SHARDING\n⚡ 性能最优",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "diamond",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "return-type-check",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 375,
      "y": 430,
      "strokeColor": "#7048e8",
      "backgroundColor": "#e5dbff",
      "width": 150,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "return-type-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 395,
      "y": 460,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 110,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📊 查询返回\n类型判断",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📊 查询返回\n类型判断",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "single-object",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 580,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#c3fae8",
      "width": 160,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "single-object-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 600,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "🎯 单点查询\nSINGLE_OBJECT\nINDEX_THEN_SCAN",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 单点查询\nSINGLE_OBJECT\nINDEX_THEN_SCAN",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "list-query",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 300,
      "y": 580,
      "strokeColor": "#f08c00",
      "backgroundColor": "#ffd43b",
      "width": 160,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "list-query-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 320,
      "y": 600,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "📋 列表查询\nLIST\nLIMITED_SCAN",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📋 列表查询\nLIST\nLIMITED_SCAN",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "aggregation-query",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 580,
      "strokeColor": "#7048e8",
      "backgroundColor": "#e5dbff",
      "width": 160,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "aggregation-query-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 520,
      "y": 600,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "📊 聚合查询\nAGGREGATION\nAGGREGATION_SCAN",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📊 聚合查询\nAGGREGATION\nAGGREGATION_SCAN",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "forbidden-query",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 700,
      "y": 580,
      "strokeColor": "#e03131",
      "backgroundColor": "#ffc9c9",
      "width": 160,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "forbidden-query-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 720,
      "y": 600,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "🚫 禁止查询\nFORBIDDEN\n排序/复合条件",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🚫 禁止查询\nFORBIDDEN\n排序/复合条件",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "diamond",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "monitoring-check",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 300,
      "y": 720,
      "strokeColor": "#c2410c",
      "backgroundColor": "#fed7aa",
      "width": 160,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "monitoring-check-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 320,
      "y": 750,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "⚠️ 监控检查\n是否触发降级？",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⚠️ 监控检查\n是否触发降级？",
      "lineHeight": 1.25,
      "baseline": 33
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "degradation-action",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 870,
      "strokeColor": "#e03131",
      "backgroundColor": "#ffc9c9",
      "width": 200,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "degradation-action-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 890,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🚨 降级处理\n• 拒绝查询\n• 返回错误提示",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🚨 降级处理\n• 拒绝查询\n• 返回错误提示",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "normal-execution",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 870,
      "strokeColor": "#2f9e44",
      "backgroundColor": "#c3fae8",
      "width": 200,
      "height": 100,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "normal-execution-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 890,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "✅ 正常执行\n• 热索引查询\n• 返回结果",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "✅ 正常执行\n• 热索引查询\n• 返回结果",
      "lineHeight": 1.25,
      "baseline": 53
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 450,
      "y": 230,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 50]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow2",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 525,
      "y": 330,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": 75,
      "height": 0,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [75, 0]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow3",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 450,
      "y": 380,
      "strokeColor": "#7048e8",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 50]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow4",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 380,
      "y": 530,
      "strokeColor": "#c2410c",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 50]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow5",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 380,
      "y": 680,
      "strokeColor": "#c2410c",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow6",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 300,
      "y": 820,
      "strokeColor": "#e03131",
      "backgroundColor": "transparent",
      "width": -100,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [-100, 50]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow7",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 460,
      "y": 820,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": 40,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [40, 50]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "yes-label1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 540,
      "y": 310,
      "strokeColor": "#2f9e44",
      "backgroundColor": "transparent",
      "width": 30,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "是",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "是",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "no-label1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 390,
      "strokeColor": "#7048e8",
      "backgroundColor": "transparent",
      "width": 30,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "否",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "否",
      "lineHeight": 1.25,
      "baseline": 13
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
