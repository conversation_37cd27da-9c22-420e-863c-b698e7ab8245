---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 30,
      "strokeColor": "#1e88e5",
      "backgroundColor": "transparent",
      "width": 200,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 24,
      "fontFamily": 5,
      "text": "🔄 支付链路数据源架构图",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔄 支付链路数据源架构图",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "legend1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 80,
      "strokeColor": "#1e88e5",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🎯 业务层: Service通过ThreadLocal设置融担编号",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 业务层: Service通过ThreadLocal设置融担编号",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "legend2",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 105,
      "strokeColor": "#9c27b0",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "⚡ AOP层: 拦截DAO方法，获取上下文路由信息",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⚡ AOP层: 拦截DAO方法，获取上下文路由信息",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "legend3",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 130,
      "strokeColor": "#388e3c",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🗄️ 数据库层: 基于counterGuaranteeNo分库，支持读写分离",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🗄️ 数据库层: 基于counterGuaranteeNo分库，支持读写分离",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "context-title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 180,
      "strokeColor": "#ff9800",
      "backgroundColor": "transparent",
      "width": 150,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🎯 上下文传递轴",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 上下文传递轴",
      "lineHeight": 1.25,
      "baseline": 16
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "context-bar",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 220,
      "strokeColor": "#ff9800",
      "backgroundColor": "#fff3e0",
      "width": 800,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "service-context",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 235,
      "strokeColor": "#1e88e5",
      "backgroundColor": "#e3f2fd",
      "width": 180,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "service-context-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 90,
      "y": 245,
      "strokeColor": "#1565c0",
      "backgroundColor": "transparent",
      "width": 140,
      "height": 10,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "ServiceContext「ThreadLocal」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "ServiceContext「ThreadLocal」",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "aop-advice",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 280,
      "y": 235,
      "strokeColor": "#9c27b0",
      "backgroundColor": "#f3e5f5",
      "width": 200,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "aop-advice-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 300,
      "y": 245,
      "strokeColor": "#7b1fa2",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 10,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "DynamicDataSourceParamsAdvice",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "DynamicDataSourceParamsAdvice",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "router-box",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 510,
      "y": 235,
      "strokeColor": "#8e24aa",
      "backgroundColor": "#f8bbd9",
      "width": 180,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "router-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 530,
      "y": 245,
      "strokeColor": "#6a1b9a",
      "backgroundColor": "transparent",
      "width": 140,
      "height": 10,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "DefaultDataSourceRouter",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "DefaultDataSourceRouter",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "route-key",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 720,
      "y": 245,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 10,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "📍 路由Key: 666_master",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📍 路由Key: 666_master",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "progress-title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 320,
      "strokeColor": "#388e3c",
      "backgroundColor": "transparent",
      "width": 150,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "📊 数据库路由进度条",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📊 数据库路由进度条",
      "lineHeight": 1.25,
      "baseline": 16
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "progress-bg",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 360,
      "strokeColor": "#e0e0e0",
      "backgroundColor": "#f5f5f5",
      "width": 800,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "progress-active",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 360,
      "strokeColor": "#4caf50",
      "backgroundColor": "#c8e6c9",
      "width": 300,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "progress-text1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 375,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 10,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "✅ 已路由完成",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "✅ 已路由完成",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "checkpoint",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 350,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [0, 60]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "checkpoint-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 360,
      "y": 375,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 10,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "📍 当前路由点",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "📍 当前路由点",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "progress-text2",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 375,
      "strokeColor": "#757575",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 10,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "⏳ 等待路由",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⏳ 等待路由",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "time-start",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 420,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "开始时间: Service层设置上下文",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "开始时间: Service层设置上下文",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "time-end",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 720,
      "y": 420,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "结束时间: 数据库连接获取完成",
      "textAlign": "right",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "结束时间: 数据库连接获取完成",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch-title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 470,
      "strokeColor": "#ff5722",
      "backgroundColor": "transparent",
      "width": 150,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🗄️ 数据库分片批次图",
      "textAlign": "left",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🗄️ 数据库分片批次图",
      "lineHeight": 1.25,
      "baseline": 16
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 520,
      "strokeColor": "#4caf50",
      "backgroundColor": "#c8e6c9",
      "width": 150,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch1-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 540,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 110,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "✅ 分库1\ncounterGuaranteeNo=666\n主库: 666_master\n从库: 666_slave_0",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "✅ 分库1\ncounterGuaranteeNo=666\n主库: 666_master\n从库: 666_slave_0",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch2",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 230,
      "y": 520,
      "strokeColor": "#4caf50",
      "backgroundColor": "#c8e6c9",
      "width": 150,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch2-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 250,
      "y": 540,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 110,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "✅ 分库2\ncounterGuaranteeNo=777\n主库: 777_master\n从库: 777_slave_0",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "✅ 分库2\ncounterGuaranteeNo=777\n主库: 777_master\n从库: 777_slave_0",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch3",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 410,
      "y": 520,
      "strokeColor": "#ff9800",
      "backgroundColor": "#fff3e0",
      "width": 150,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch3-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 430,
      "y": 540,
      "strokeColor": "#f57c00",
      "backgroundColor": "transparent",
      "width": 110,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "🔄 分库3\ncounterGuaranteeNo=888\n主库: 888_master\n从库: 888_slave_0",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔄 分库3\ncounterGuaranteeNo=888\n主库: 888_master\n从库: 888_slave_0",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch4",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "dashed",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 590,
      "y": 520,
      "strokeColor": "#9e9e9e",
      "backgroundColor": "#f5f5f5",
      "width": 150,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "batch4-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 610,
      "y": 540,
      "strokeColor": "#757575",
      "backgroundColor": "transparent",
      "width": 110,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "⏳ 分库4\ncounterGuaranteeNo=999\n待初始化",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⏳ 分库4\ncounterGuaranteeNo=999\n待初始化",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "more-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 770,
      "y": 560,
      "strokeColor": "#9e9e9e",
      "backgroundColor": "transparent",
      "width": 30,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "...",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "...",
      "lineHeight": 1.25,
      "baseline": 14
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
