---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==


# Text Elements
DynamicDataSource → ShardingSphere-JDBC 平滑迁移 ^title-text

现有方案

• DynamicDataSource
• 只支持分库
• 自研维护成本高 ^current-text

核心问题

• 需要分表能力
• 架构冲突
• 无法共存 ^problem-text

解决方案

• ShardingSphere-JDBC
• 支持分库分表
• 企业级特性 ^solution-text

第一阶段：平滑迁移「只做分库，不做分表」 ^phase1-title-text

多配置源统一管理

• Nacos配置中心
• APS Dubbo服务
• 配置合并策略
• 编程式构建 ^config-text

统一AOP路由机制

• ServiceContext获取
• HintManager路由
• 融担号分库算法
• 业务代码零侵入 ^aop-text

功能对等性验证

• 路由一致性测试
• 性能对比测试
• 事务一致性测试
• 迁移验证机制 ^validation-text

架构统一收益

• 技术栈标准化
• 分表能力准备
• 企业级特性
• 平滑过渡保障 ^benefits-text

第二阶段：库内分表改造
「在第一阶段基础上增加分表能力」 ^phase2-text

# Drawing
```json
{
	"type": "excalidraw",
	"version": 2,
	"source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
	"elements": [
		{
			"type": "rectangle",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "title-rect",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 200,
			"y": 50,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "#a5d8ff",
			"width": 400,
			"height": 60,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": {
				"type": 3
			},
			"boundElements": [
				{
					"id": "title-text",
					"type": "text"
				}
			],
			"updated": 1,
			"link": null,
			"locked": false
		},
		{
			"type": "text",
			"version": 1,
			"versionNonce": 1,
			"isDeleted": false,
			"id": "title-text",
			"fillStyle": "solid",
			"strokeWidth": 2,
			"strokeStyle": "solid",
			"roughness": 1,
			"opacity": 100,
			"angle": 0,
			"x": 220,
			"y": 70,
			"strokeColor": "#1e1e1e",
			"backgroundColor": "transparent",
			"width": 360,
			"height": 25,
			"seed": 1,
			"groupIds": [],
			"frameId": null,
			"roundness": null,
			"boundElements": [],
			"updated": 1,
			"link": null,
			"locked": false,
			"fontSize": 20,
			"fontFamily": 1,
			"text": "DynamicDataSource → ShardingSphere-JDBC 平滑迁移",
			"textAlign": "center",
			"verticalAlign": "middle",
			"containerId": "title-rect",
			"originalText": "DynamicDataSource → ShardingSphere-JDBC 平滑迁移",
			"lineHeight": 1.25,
			"baseline": 18
		}
	],
	"appState": {
		"theme": "light",
		"viewBackgroundColor": "#ffffff",
		"currentItemStrokeColor": "#1e1e1e",
		"currentItemBackgroundColor": "transparent",
		"currentItemFillStyle": "solid",
		"currentItemStrokeWidth": 2,
		"currentItemStrokeStyle": "solid",
		"currentItemRoughness": 1,
		"currentItemOpacity": 100,
		"currentItemFontFamily": 1,
		"currentItemFontSize": 20,
		"currentItemTextAlign": "left",
		"currentItemStartArrowhead": null,
		"currentItemEndArrowhead": "arrow",
		"scrollX": 0,
		"scrollY": 0,
		"zoom": {
			"value": 1
		},
		"currentItemRoundness": "round",
		"gridSize": null,
		"gridColor": {
			"Bold": "#C9C9C9FF",
			"Regular": "#EDEDEDFF"
		},
		"currentStrokeOptions": null,
		"previousGridSize": null,
		"frameRendering": {
			"enabled": true,
			"clip": true,
			"name": true,
			"outline": true
		}
	},
	"files": {}
}
```
%%
