@startuml
!theme materia
' Use ortho lines for a cleaner look
skinparam linetype ortho

package "AOP & Execution Core" {
    class ShardingIndexAspect <<Aspect>> {
        -executionEngine: IShardingExecutionEngine
        +around(ProceedingJoinPoint)
    }

    interface IShardingExecutionEngine {
        +execute(QueryContext): Object
    }

    class ShardingExecutionEngine <<Service>> {
        -operationDetector: IOperationTypeDetector
        -writeHandler: IWriteOperationHandler
        -readHandler: IReadOperationHandler
        +execute(QueryContext): Object
    }

    class QueryContext <<DTO>> {
        -sql: String
        -methodArgs: Object[]
    }

    interface IOperationTypeDetector {
        +detect(QueryContext): OperationType
    }
    class SqlOperationTypeDetector <<Component>>

    ShardingIndexAspect --> IShardingExecutionEngine : uses
    ShardingExecutionEngine .up.|> IShardingExecutionEngine : implements
    ShardingExecutionEngine --> IOperationTypeDetector
    ShardingExecutionEngine --> IWriteOperationHandler
    ShardingExecutionEngine --> IReadOperationHandler

    SqlOperationTypeDetector .up.|> IOperationTypeDetector : implements

    ShardingIndexAspect ..> QueryContext : creates
    IShardingExecutionEngine ..> QueryContext : uses
}


package "Write Strategies (Decorator)" {
    interface IWriteOperationHandler {
        +handle(QueryContext): Object
    }

    abstract class AbstractWriteStrategy {
        #preWriteProcess(QueryContext)
    }

    class StandardWriteStrategy <<Component>> {
        +handle(QueryContext): Object
    }

    class DualWriteTransitionalStrategy <<Decorator>> {
        -standardWriteStrategy: StandardWriteStrategy
        -legacyTableDao: ILegacyTableDao
        +handle(QueryContext): Object
    }

    IWriteOperationHandler <|.. AbstractWriteStrategy
    AbstractWriteStrategy <|-- StandardWriteStrategy
    IWriteOperationHandler <|.. DualWriteTransitionalStrategy
    DualWriteTransitionalStrategy o-> StandardWriteStrategy : decorates
}


package "Read Strategies (Decorator)" {
    interface IReadOperationHandler {
        +handle(QueryContext): Object
    }

    abstract class AbstractReadStrategy {
        #makeRoutingDecision(QueryContext)
    }

    class ShardingReadStrategy <<Component>> {
        +handle(QueryContext): Object
    }

    class GrayscaleReadTransitionalStrategy <<Decorator>> {
        -shardingReadStrategy: ShardingReadStrategy
        -legacyTableDao: ILegacyTableDao
        +handle(QueryContext): Object
    }

    IReadOperationHandler <|.. AbstractReadStrategy
    AbstractReadStrategy <|-- ShardingReadStrategy
    IReadOperationHandler <|.. GrayscaleReadTransitionalStrategy
    GrayscaleReadTransitionalStrategy o-> ShardingReadStrategy : decorates
}


package "Configuration & Factory" {
    interface IStrategyFactory {
        +createWriteStrategy(): IWriteOperationHandler
        +createReadStrategy(): IReadOperationHandler
    }

    class ConditionalStrategyFactory <<Component>> {
        -strategyConfig: IShardingStrategyConfig
        +createWriteStrategy(): IWriteOperationHandler
        +createReadStrategy(): IReadOperationHandler
    }

    interface IShardingStrategyConfig {
        +isDualWriteEnabled(): boolean
        +getReadPathMode(): String
    }

    class DynamicShardingStrategyConfig <<Component>>

    IStrategyFactory <|.. ConditionalStrategyFactory
    ConditionalStrategyFactory --> IShardingStrategyConfig : uses
    DynamicShardingStrategyConfig .up.|> IShardingStrategyConfig : implements

    ConditionalStrategyFactory ..> IWriteOperationHandler : creates
    ConditionalStrategyFactory ..> IReadOperationHandler : creates
}

@enduml 