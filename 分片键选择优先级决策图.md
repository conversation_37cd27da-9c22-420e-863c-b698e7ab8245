---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 30,
      "strokeColor": "#1e88e5",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 24,
      "fontFamily": 5,
      "text": "🎯 分片键选择优先级决策图",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎯 分片键选择优先级决策图",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "decision-center",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 80,
      "strokeColor": "#1e88e5",
      "backgroundColor": "#e3f2fd",
      "width": 300,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "decision-center-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 450,
      "y": 100,
      "strokeColor": "#1565c0",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🎛️ 分片键选择决策中心",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🎛️ 分片键选择决策中心",
      "lineHeight": 1.25,
      "baseline": 16
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "split-line",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 140,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 300,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [-300, 300]
      ]
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "split-line2",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 500,
      "y": 140,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 300,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [300, 300]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "service-level",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 180,
      "strokeColor": "#4caf50",
      "backgroundColor": "#c8e6c9",
      "width": 300,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "service-level-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 200,
      "y": 200,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🥇 服务级别 (优先选择)\n统一分片键 - 贯穿全链路\nloanOrderId",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🥇 服务级别 (优先选择)\n统一分片键 - 贯穿全链路\nloanOrderId",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table-level",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 600,
      "y": 180,
      "strokeColor": "#ff9800",
      "backgroundColor": "#fff3e0",
      "width": 300,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table-level-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 700,
      "y": 200,
      "strokeColor": "#f57c00",
      "backgroundColor": "transparent",
      "width": 100,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🥈 单表级别 (降级方案)\n库内分表键 - 表特定\nprojectNo / cust_no",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🥈 单表级别 (降级方案)\n库内分表键 - 表特定\nprojectNo / cust_no",
      "lineHeight": 1.25,
      "baseline": 32
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table1-service",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 300,
      "strokeColor": "#4caf50",
      "backgroundColor": "#e8f5e8",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table1-service-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 315,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "✅ t_trans_order\nloanOrderId\n🟢 健康",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "✅ t_trans_order\nloanOrderId\n🟢 健康",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table2-service",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 190,
      "y": 300,
      "strokeColor": "#4caf50",
      "backgroundColor": "#e8f5e8",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table2-service-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 210,
      "y": 315,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "✅ t_withhold_order\nloanOrderId\n🟢 健康",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "✅ t_withhold_order\nloanOrderId\n🟢 健康",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table3-service",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 330,
      "y": 300,
      "strokeColor": "#4caf50",
      "backgroundColor": "#e8f5e8",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table3-service-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 350,
      "y": 315,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "✅ t_pay_order\nloanOrderId\n🟢 健康",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "✅ t_pay_order\nloanOrderId\n🟢 健康",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table4-mixed",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 550,
      "y": 300,
      "strokeColor": "#ff9800",
      "backgroundColor": "#fff8e1",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table4-mixed-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 570,
      "y": 315,
      "strokeColor": "#f57c00",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "⚠️ t_project_info\nprojectNo\n🟡 注意",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⚠️ t_project_info\nprojectNo\n🟡 注意",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table5-table",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 690,
      "y": 300,
      "strokeColor": "#ff9800",
      "backgroundColor": "#fff8e1",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table5-table-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 710,
      "y": 315,
      "strokeColor": "#f57c00",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "⚠️ t_customer_info\ncust_no\n🟡 注意",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⚠️ t_customer_info\ncust_no\n🟡 注意",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table6-problem",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 830,
      "y": 300,
      "strokeColor": "#f44336",
      "backgroundColor": "#ffebee",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "table6-problem-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 850,
      "y": 315,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 80,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "❌ t_config_info\n无合适分片键\n🔴 问题",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "❌ t_config_info\n无合适分片键\n🔴 问题",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "flow-arrow1",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 110,
      "y": 360,
      "strokeColor": "#4caf50",
      "backgroundColor": "transparent",
      "width": 60,
      "height": 0,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [60, 0]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "flow-arrow2",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 250,
      "y": 360,
      "strokeColor": "#4caf50",
      "backgroundColor": "transparent",
      "width": 60,
      "height": 0,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [60, 0]
      ]
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "break-line1",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "dashed",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 610,
      "y": 360,
      "strokeColor": "#ff9800",
      "backgroundColor": "transparent",
      "width": 60,
      "height": 0,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [60, 0]
      ]
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "break-line2",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "dashed",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 750,
      "y": 360,
      "strokeColor": "#ff9800",
      "backgroundColor": "transparent",
      "width": 60,
      "height": 0,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [60, 0]
      ]
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "break-line3",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 890,
      "y": 350,
      "strokeColor": "#f44336",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [0, 20]
      ]
    },
    {
      "type": "line",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "break-line4",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 880,
      "y": 360,
      "strokeColor": "#f44336",
      "backgroundColor": "transparent",
      "width": 20,
      "height": 0,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": null,
      "points": [
        [0, 0],
        [20, 0]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "flow-label1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 120,
      "y": 380,
      "strokeColor": "#4caf50",
      "backgroundColor": "transparent",
      "width": 40,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "数据流畅",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "数据流畅",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "flow-label2",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 620,
      "y": 380,
      "strokeColor": "#ff9800",
      "backgroundColor": "transparent",
      "width": 40,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "需要处理",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "需要处理",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "flow-label3",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 860,
      "y": 380,
      "strokeColor": "#f44336",
      "backgroundColor": "transparent",
      "width": 60,
      "height": 15,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 12,
      "fontFamily": 5,
      "text": "数据链路断开",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "数据链路断开",
      "lineHeight": 1.25,
      "baseline": 9
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "priority-pyramid",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 100,
      "y": 450,
      "strokeColor": "#666666",
      "backgroundColor": "transparent",
      "width": 800,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "📊 优先级金字塔:\n🥇 服务级统一分片键 (loanOrderId) → 🟢 最优选择，数据链路完整，跨表查询高效\n🥈 库内分表键 (projectNo/cust_no) → 🟡 次优选择，需要额外处理，部分业务场景受限\n🥉 无合适分片键 → 🔴 问题状态，数据孤岛严重，需要业务改造或技术方案优化\n\n💡 决策原则: 优先使用服务级统一分片键，在单个服务内部寻找一个黄金业务分表键，能上最高的核心查询和修改都能通过分表键准确路由",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "📊 优先级金字塔:\n🥇 服务级统一分片键 (loanOrderId) → 🟢 最优选择，数据链路完整，跨表查询高效\n🥈 库内分表键 (projectNo/cust_no) → 🟡 次优选择，需要额外处理，部分业务场景受限\n🥉 无合适分片键 → 🔴 问题状态，数据孤岛严重，需要业务改造或技术方案优化\n\n💡 决策原则: 优先使用服务级统一分片键，在单个服务内部寻找一个黄金业务分表键，能上最高的核心查询和修改都能通过分表键准确路由",
      "lineHeight": 1.25,
      "baseline": 72
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
