---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 30,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 30,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 24,
      "fontFamily": 5,
      "text": "⚡ 分表需求驱动的技术选型冲突",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⚡ 分表需求驱动的技术选型冲突",
      "lineHeight": 1.25,
      "baseline": 23
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "common-start",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 450,
      "y": 100,
      "strokeColor": "#1e88e5",
      "backgroundColor": "#e3f2fd",
      "width": 200,
      "height": 50,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "common-start-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 470,
      "y": 115,
      "strokeColor": "#1565c0",
      "backgroundColor": "transparent",
      "width": 160,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "业务请求 + 融担编号设置\n「共同起点」",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "业务请求 + 融担编号设置\n「共同起点」",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow-down1",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 550,
      "y": 150,
      "strokeColor": "#1565c0",
      "backgroundColor": "transparent",
      "width": 0,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [0, 40]
      ]
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "decision-point",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 190,
      "strokeColor": "#ff5722",
      "backgroundColor": "#ffccbc",
      "width": 300,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "decision-point-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 420,
      "y": 210,
      "strokeColor": "#d84315",
      "backgroundColor": "transparent",
      "width": 260,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "🔥 技术选型决策点：如何实现分表能力？",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🔥 技术选型决策点：如何实现分表能力？",
      "lineHeight": 1.25,
      "baseline": 14
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow-left",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 280,
      "strokeColor": "#43a047",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [-120, 60]
      ]
    },
    {
      "type": "arrow",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "arrow-right",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 700,
      "y": 280,
      "strokeColor": "#8e24aa",
      "backgroundColor": "transparent",
      "width": 120,
      "height": 60,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 2
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "startBinding": null,
      "endBinding": null,
      "lastCommittedPoint": null,
      "startArrowhead": null,
      "endArrowhead": "arrow",
      "points": [
        [0, 0],
        [120, 60]
      ]
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "conflict-x",
      "fillStyle": "solid",
      "strokeWidth": 4,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 530,
      "y": 300,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 40,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 36,
      "fontFamily": 5,
      "text": "❌",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "❌",
      "lineHeight": 1.25,
      "baseline": 29
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "conflict-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 480,
      "y": 350,
      "strokeColor": "#d32f2f",
      "backgroundColor": "transparent",
      "width": 140,
      "height": 20,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "⚠️ 两种方案互斥冲突\n无法同时使用",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⚠️ 两种方案互斥冲突\n无法同时使用",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "dynamic-path",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 50,
      "y": 400,
      "strokeColor": "#43a047",
      "backgroundColor": "#e8f5e8",
      "width": 280,
      "height": 300,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "dynamic-title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 420,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 240,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "🛠️ 方案A: DynamicDataSource",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "🛠️ 方案A: DynamicDataSource",
      "lineHeight": 1.25,
      "baseline": 16
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "dynamic-content",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 70,
      "y": 460,
      "strokeColor": "#2e7d32",
      "backgroundColor": "transparent",
      "width": 240,
      "height": 220,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "📋 技术特点:\n• 保留现有分库逻辑\n• 新增MyBatis拦截器\n• SQL表名动态替换\n\n✅ 优势:\n• 代码侵入性极低\n• 实现速度快\n• 风险可控，可回滚\n• 不触及现有架构\n\n❌ 劣势:\n• 功能局限性大\n• 不支持复杂查询\n• 非分片键查询困难\n• 长期维护成本高\n\n🎯 适用场景:\n快速解决性能瓶颈",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "📋 技术特点:\n• 保留现有分库逻辑\n• 新增MyBatis拦截器\n• SQL表名动态替换\n\n✅ 优势:\n• 代码侵入性极低\n• 实现速度快\n• 风险可控，可回滚\n• 不触及现有架构\n\n❌ 劣势:\n• 功能局限性大\n• 不支持复杂查询\n• 非分片键查询困难\n• 长期维护成本高\n\n🎯 适用场景:\n快速解决性能瓶颈",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sharding-path",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 770,
      "y": 400,
      "strokeColor": "#8e24aa",
      "backgroundColor": "#f3e5f5",
      "width": 280,
      "height": 300,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sharding-title",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 790,
      "y": 420,
      "strokeColor": "#6a1b9a",
      "backgroundColor": "transparent",
      "width": 240,
      "height": 25,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "⚡ 方案B: ShardingSphere-JDBC",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "⚡ 方案B: ShardingSphere-JDBC",
      "lineHeight": 1.25,
      "baseline": 16
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "sharding-content",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 790,
      "y": 460,
      "strokeColor": "#6a1b9a",
      "backgroundColor": "transparent",
      "width": 240,
      "height": 220,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "📋 技术特点:\n• 完全替换DynamicDataSource\n• 统一分库分表管理\n• 企业级分片框架\n\n✅ 优势:\n• 功能强大且全面\n• 支持复杂查询聚合\n• 对业务代码无侵入\n• 维护成本低\n• 性能优异\n\n❌ 劣势:\n• 替换核心组件风险高\n• 需要重新实现分库逻辑\n• 学习成本较高\n• 一次性切换难度大\n\n🎯 适用场景:\n构建长期健康架构",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "📋 技术特点:\n• 完全替换DynamicDataSource\n• 统一分库分表管理\n• 企业级分片框架\n\n✅ 优势:\n• 功能强大且全面\n• 支持复杂查询聚合\n• 对业务代码无侵入\n• 维护成本低\n• 性能优异\n\n❌ 劣势:\n• 替换核心组件风险高\n• 需要重新实现分库逻辑\n• 学习成本较高\n• 一次性切换难度大\n\n🎯 适用场景:\n构建长期健康架构",
      "lineHeight": 1.25,
      "baseline": 11
    },
    {
      "type": "rectangle",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "conflict-box",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 380,
      "y": 750,
      "strokeColor": "#d32f2f",
      "backgroundColor": "#ffebee",
      "width": 340,
      "height": 120,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": {
        "type": 3
      },
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "conflict-detail",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 770,
      "strokeColor": "#c62828",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 80,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🔥 核心冲突分析:\n\n• DynamicDataSource多数据源管理方案与\n  ShardingSphere数据源管理方案无法共存\n\n• 两者都需要接管数据源路由决策权\n\n• 必须完全替换，无法渐进式迁移",
      "textAlign": "left",
      "verticalAlign": "top",
      "containerId": null,
      "originalText": "🔥 核心冲突分析:\n\n• DynamicDataSource多数据源管理方案与\n  ShardingSphere数据源管理方案无法共存\n\n• 两者都需要接管数据源路由决策权\n\n• 必须完全替换，无法渐进式迁移",
      "lineHeight": 1.25,
      "baseline": 13
    },
    {
      "type": "text",
      "version": 1,
      "versionNonce": 1,
      "isDeleted": false,
      "id": "solution-text",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "angle": 0,
      "x": 400,
      "y": 900,
      "strokeColor": "#ff9800",
      "backgroundColor": "transparent",
      "width": 300,
      "height": 40,
      "seed": 1,
      "groupIds": [],
      "frameId": null,
      "roundness": null,
      "boundElements": [],
      "updated": 1,
      "link": null,
      "locked": false,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "💡 解决方案: 两阶段演进式架构路线\n第一阶段用方案A快速解决问题\n第二阶段用方案B构建长期架构",
      "textAlign": "center",
      "verticalAlign": "middle",
      "containerId": null,
      "originalText": "💡 解决方案: 两阶段演进式架构路线\n第一阶段用方案A快速解决问题\n第二阶段用方案B构建长期架构",
      "lineHeight": 1.25,
      "baseline": 32
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
